# import logging.config
from pathlib import Path

from fastapi import <PERSON>AP<PERSON>
from fastapi.exceptions import RequestValidationError
from fastapi.middleware.cors import CORSMiddleware
from fastapi.requests import Request
from fastapi.responses import UJSONResponse

from app import settings
from app.helper.response_helper import BaseResponse
from app.lifetime import lifespan
from app.middleware import profiler
from app.middleware.cors_middleware import CustomCORSMiddleware
from app.web import monitor
from app.web.router import api_router

APP_ROOT = Path(__file__).parent.parent


def get_app() -> FastAPI:
    """
    Get FastAPI application.

    This is the main constructor of an application.

    :return: application.
    """
    app = FastAPI(
        title="Sure AI ML API",
        description="Matching API for the Employers to search the candidates",
        version="1.0",
        docs_url=None if settings.environment == "production" else "/docs",
        redoc_url=None if settings.environment == "production" else "/redoc",
        openapi_url="/api/openapi.json",
        default_response_class=UJSONResponse,
        lifespan=lifespan,
    )

    # Custom request exception handler
    @app.exception_handler(RequestValidationError)
    async def custom_validation_exception_handler(
        request: Request, exc: RequestValidationError
    ):
        return await BaseResponse.request_exception_response(request, exc)

    # Add middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=False,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    app.add_middleware(middleware_class=profiler.ProfilerMiddleware)
    app.add_middleware(CustomCORSMiddleware)

    # Main router for the API.
    app.include_router(router=monitor.router)
    app.include_router(router=api_router, prefix="/sureai-ml/api")

    return app
