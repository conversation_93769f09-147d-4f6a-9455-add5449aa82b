from openai import AuthenticationError, BadRequestError, RateLimitError

from app import logger


class OpenAIAuthError(Exception):
    """Raised when OpenAI authentication fails."""

    pass


class OpenAIRateLimitError(Exception):
    """Raised when OpenAI rate limit is exceeded."""

    pass


class OpenAIBadRequestError(Exception):
    """Raised for OpenAI bad requests."""

    pass


def handle_llm_exception(e: Exception, context: str = "LLM operation"):
    if isinstance(e, AuthenticationError):
        logger.error(f"❌ {context}: Invalid or missing OpenAI API key.")
        raise OpenAIAuthError("Invalid or missing OpenAI API key.")

    elif isinstance(e, RateLimitError):
        logger.error(f"❌ {context}: Rate limit exceeded.")
        raise OpenAIRateLimitError("Rate limit exceeded. Please wait before retrying.")

    elif isinstance(e, BadRequestError):
        logger.error(f"❌ {context}: Bad request - {e}")
        raise OpenAIBadRequestError(f"Bad request: {e}")

    else:
        logger.error(f"❌ {context}: Unexpected error - {e}")
        raise Exception(f"Unexpected error: {e}")
