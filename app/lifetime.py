from asyncio import current_task
from contextlib import asynccontextmanager

from fastapi import FastAPI
from sqlalchemy.ext.asyncio import (
    AsyncSession,
    async_scoped_session,
    create_async_engine,
)
from sqlalchemy.orm import sessionmaker

from app import logger
from app.settings import settings


def _setup_db(app: FastAPI) -> None:  # pragma: no cover
    """
    Creates connection to the database.

    This function creates SQLAlchemy engine instance,
    session_factory for creating sessions
    and stores them in the application's state property.

    :param app: fastAPI application.
    """
    engine = create_async_engine(str(settings.db_url), echo=settings.db_echo)

    session_factory = async_scoped_session(
        sessionmaker(
            engine,
            expire_on_commit=False,
            class_=AsyncSession,
        ),
        scopefunc=current_task,
    )
    app.state.db_engine = engine
    app.state.db_session_factory = session_factory
    logger.info("Database engine and session factory initialized")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Lifespan context handler for FastAPI app startup and shutdown.

    Replaces the deprecated @app.on_event startup/shutdown handlers.

    :param app: the FastAPI application.
    """
    _setup_db(app)
    logger.info("Database setup completed on startup")

    yield  # Run the application

    await app.state.db_engine.dispose()
    logger.info("Database engine disposed on shutdown")
