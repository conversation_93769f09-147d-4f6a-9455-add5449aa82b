import sys
from pathlib import Path

from loguru import logger

from app.settings import settings

logger.level("DEBUG", color="<cyan><bold>")
logger.level("INFO", color="<blue><bold>")
logger.level("SUCCESS", color="<green><bold>")
logger.level("WARNING", color="<yellow><bold>")
logger.level("ERROR", color="<red><bold>")
logger.level("CRITICAL", color="<red><bold><blink>")

# Add file logger for production
if settings.environment == "production":
    log_path = Path("logs")
    log_path.mkdir(exist_ok=True)
    logger.add(
        log_path / "app.log",
        rotation="500 MB",
        retention="10 days",
        compression="zip",
        format=(
            "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | "
            "{extra[trace_id]} | {name}:{function}:{line} - {message}"
        ),
        level=settings.log_level,
        backtrace=True,
        diagnose=True,
    )


def get_logger(name: str):
    """Get a logger instance with the given name and trace ID."""
    return logger.bind(name=name)


def setup_logging():
    """
    Configure logging for the application using Loguru
    """
    # Remove default handler
    logger.remove()

    # Add console handler with custom format
    logger.add(
        sys.stdout,
        format=(
            "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
            "<level>{level: <8}</level> "
            "| <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
            "<level>{message}</level>"
        ),
        level="INFO",
        colorize=True,
    )

    # Add file handler for errors
    logger.add(
        "logs/error.log",
        format=(
            "{time:YYYY-MM-DD HH:mm:ss.SSS} | "
            "{level: <8} | {name}:{function}:{line} - {message}"
        ),
        level="ERROR",
        rotation="500 MB",
        retention="10 days",
    )

    # Add file handler for all logs
    logger.add(
        "logs/app.log",
        format=(
            "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} "
            "| {name}:{function}:{line} - {message}"
        ),
        level="DEBUG",
        rotation="100 MB",
        retention="7 days",
    )

    # Set log level based on environment
    if settings.environment == "dev":
        logger.level("DEBUG")
    else:
        logger.level("INFO")

    return logger
