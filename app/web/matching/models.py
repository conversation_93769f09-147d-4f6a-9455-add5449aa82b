from enum import Enum
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field

# Used for the Request body that will be received from the backend
class MatchingRequest(BaseModel):
    prompt: str = Field(..., description="Natural language query for candidate matching")
    uuid: str = Field(...,description="UUID for the sourcing partner"
    )
    language: str = Field(default = "en")
    conversation_uuid: Optional[str]

# Enum for flag
class MatchFlagEnum(int, Enum):
    CANDIDATE_NOT_MATCHED = 0
    EMPTY_RESULTS_FROM_SP = 1
    CANDIDATE_MATCHED = 2

# Step 1: Used for the parsed query for the structured output (Entity Extraction)
class ParsedQuery(BaseModel):
    profession: Optional[str] = Field(
        None, description="The profession or job title mentioned in the query"
    )
    experience_years: Optional[Union[str, int]] = Field(
        None,
        description="Years of experience required, e.g., '5+', 'more than 3', '5 plus', 'at least 2 years', 'less than 2 years",
    )
    languages: Optional[str] = Field(
        None,
        description="List of languages mentioned in the query, such as German, French, or any globally spoken language."
    )
    language_proficiency: Optional[str] = Field(
        None,
        description="Language proficiency level explicitly mentioned in the query. Consider the following levels only: A1, A2, B1, B2, C1, C2, Native."
    )
    language_proficiency_condition: Optional[str] = Field(
        None,
        description="Condition or operator for proficiency level. Use SQL-style notation: 'At least' → '>=', 'At most' → '<=', 'More than' → '>', 'Less than' → '<', 'Exactly' → '=', 'Not' → '!='."
    )
    skills: Optional[List[str]] = Field(
        None,
        description="List of type of skills mentioned in the query or in the profession such as ICU",
    )
    education: Optional[List[str]] = Field(
        None,
        description="Highest education level mentioned in the query (e.g., Bachelor's, Master's, PhD).",
    )
    certification: Optional[List[str]] = Field(
        None,
        description="Certifications associated with the profession, that might be similar with the profession.",
    )
    gender: Optional[str] = Field(
        None,
        description="Gender preference if explicitly mentioned (e.g., Male, Female, Non-binary).",
    )

# Step 2: Used for the batch profile extraction for the structured output (Profile Extraction)
class CandidateSkillResult(BaseModel):
    candidate_id: str = Field(..., description="Unique identifier for the candidate")
    batch_skills: List[str] = Field(..., description="A list of up to 3 matched skills")
    batch_education_level: List[str] = Field(
        None,
        description="All the education levels mentioned in the profile data (e.g., Bachelor's, Master's, PhD).",
    )
    batch_certification: List[str] = Field(
        None,
        description="All the certifications mentioned in the profile data (e.g., PMP, AWS Certified Solutions Architect).",
    )
    batch_total_experience_years: Optional[float] = Field(
        None,
        description="Total experience years mentioned in the profile data (e.g., 5 years, 10 years, etc.).",
    )
    batch_gender: Optional[str] = Field(
        None,
        description="Gender preference if explicitly mentioned (e.g., Male, Female, Non-binary).",
    )
    batch_language_proficiency: Optional[str] = Field(
        None,
        description="Language proficiency level mentioned in the profile data (e.g., A1, A2, B1, B2, C1, C2, Native).",
    )
    batch_language: Optional[str] = Field(
        None,
        description="Language type mentioned in the profile data.",
    )

# Used for the batch skill extraction for the structured output
class BatchSkillResult(BaseModel):
    candidates: List[CandidateSkillResult] = Field(
        ..., description="List of candidate skill results"
    )

# Step 3: Used for the matching score for the structured output (Matching Score)
class CandidateMatchingScore(BaseModel):
    uuid: Optional[str] = Field(None, description="Unique identifier for the candidate")
    name: Optional[str] = Field(None, description="Candidate's name")
    skill_score: Optional[float] = Field(None, description="Weighted score for skills component")
    experience_years_score: Optional[float] = Field(None, description="Weighted score for experience component")
    language_proficiency_score: Optional[float] = Field(None, description="Weighted score for language proficiency component")
    education_level_score: Optional[float] = Field(None, description="Weighted score for education level component")
    certification_score: Optional[float] = Field(None, description="Weighted score for certification component")
    total_score: float = Field(..., description="Total matching score for the candidate (rounded)")

class MatchingPromptResult(BaseModel):
    candidates: list[CandidateMatchingScore] = Field(..., description="List of candidates with component and total scores")

# Response structure to be saved in the 'result' and send back to the backend
class CandidateResult(BaseModel):
    uuid: Optional[str]
    name: Optional[str]
    profile_photo: Optional[str]
    total_experience_years: Optional[float]
    country: Optional[str]
    state: Optional[str]
    city: Optional[str]
    profession: Optional[str]
    skills: List[str]
    education: List[str]
    certification: List[str]
    language: Optional[str]
    language_proficiency: Optional[str]
    gender: Optional[str]
    matching_score: Dict[str, Optional[float]]
    total_score: float

# Final response to be returned to the backend
class MatchingResult(BaseModel):
    prompt: str
    candidates: List[CandidateResult]
    token_usage_report: Dict[str, Any]
    match_flag: MatchFlagEnum = MatchFlagEnum.CANDIDATE_NOT_MATCHED
    message: str = ""
