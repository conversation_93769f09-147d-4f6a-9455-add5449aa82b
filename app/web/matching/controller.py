from fastapi import Depends, Request, status
from fastapi_utils.cbv import cbv
from fastapi_utils.inferring_router import InferringRouter

from app import logger
from app.exception.llm_exceptions import (OpenAIAuthError,
                                        OpenAIBadRequestError,
                                        OpenAIRateLimitError)
from app.helper.llm_helper import LLMResponse
from app.helper.response_helper import BaseResponse
from app.web.matching.models import MatchingRequest
from app.web.matching.response import MatchingResponse
from app.web.matching.service import MatchingService

router = InferringRouter()


def get_matching_service(request: Request) -> MatchingService:
    """Dependency to get matching service with database session factory"""
    return MatchingService(request.app.state.db_session_factory)


@cbv(router)
class MatchingController:
    @router.post("/response")
    async def match_candidates(
        self,
        request: MatchingRequest,
        matching_service: MatchingService = Depends(get_matching_service),
    ) -> MatchingResponse:
        """
        Match candidates based on natural language query.

        This endpoint processes a natural language query to find matching candidates,
        extracts skills, calculates matching scores, and saves results to a JSON file.
        """
        try:
            logger.info(f"🚀 Starting search for: {request.prompt}")

            result = await matching_service.process_matching_request(
                query=request.prompt, uuid=request.uuid
            )

            return MatchingResponse(
                status_code=status.HTTP_200_OK,
                message=result.message,
                payload={
                    "total_candidates": len(result.candidates),
                    "conversation_uuid": request.conversation_uuid,
                    "prompt": result.prompt,
                    "result": [c.model_dump() for c in result.candidates],
                    "token_usage": result.token_usage_report["totals"],
                    "match_flag": result.match_flag,
                },
            )

        except OpenAIAuthError as e:
            logger.error(f"OpenAI Auth Error: {e}")
            return await LLMResponse.openai_unauthorized_response()
        except OpenAIRateLimitError as e:
            logger.error(f"OpenAI Rate Limit Error: {e}")
            return await LLMResponse.openai_rate_limit_response()
        except OpenAIBadRequestError as e:
            logger.error(f"OpenAI Bad Request Error: {e}")
            return await BaseResponse.bad_request_response(str(e))
        except Exception as e:
            logger.error(f"Error in candidate matching: {e}")
            return await BaseResponse.server_error_response()
