import sys
from langchain.callbacks.base import As<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app import logger


def calculate_gpt_4_1_cost(input_tokens, output_tokens):
    """Calculate the cost with correct pricing."""
    # GPT-4.1
    input_cost = (input_tokens / 1_000_000) * 2.00
    output_cost = (output_tokens / 1_000_000) * 8.00

    total_cost = input_cost + output_cost
    return total_cost


class TokenUsageCallbackHandler(AsyncCallbackHandler):
    def __init__(self):
        self.total_input = 0
        self.total_output = 0
        self.total_tokens = 0
        self.entity_extraction_tokens = {
            "input": 0,
            "output": 0,
            "cost": 0.0,
        }
        self.skill_matching_tokens = {
            "input": 0,
            "output": 0,
            "cost": 0.0,
        }
        self.current_operation = "entity_extraction"
        self.final_matching_tokens = {
            "input": 0,
            "output": 0,
            "cost": 0.0,
        }

    def set_operation(self, operation):
        self.current_operation = operation

    async def on_llm_start(self, serialized, prompts, **kwargs):
        try:
            if prompts:
                if isinstance(prompts[0], str):
                    self.current_input_text = prompts[0]
                elif hasattr(prompts[0], "text"):
                    self.current_input_text = prompts[0].text
                elif hasattr(prompts[0], "content"):
                    self.current_input_text = str(prompts[0].content)
                else:
                    self.current_input_text = str(prompts[0])
        except Exception as e:
            logger.error(f"Error capturing input text: {e}")
            self.current_input_text = "[Could not capture input text]"

    async def on_llm_end(self, response, **kwargs):
        try:
            usage = getattr(response, "llm_output", {}) or {}
            token_data = usage.get("token_usage", {}) or {}
            input_tokens = token_data.get("prompt_tokens", 0)
            output_tokens = token_data.get("completion_tokens", 0)
            total_tokens = input_tokens + output_tokens  # New

            cost = calculate_gpt_4_1_cost(input_tokens, output_tokens)

            self.total_input += input_tokens
            self.total_output += output_tokens
            self.total_tokens += total_tokens

            if self.current_operation == "entity_extraction":
                self.entity_extraction_tokens["input"] += input_tokens
                self.entity_extraction_tokens["output"] += output_tokens
                self.entity_extraction_tokens["cost"] += cost
            elif self.current_operation == "final_matching":
                self.final_matching_tokens["input"] += input_tokens
                self.final_matching_tokens["output"] += output_tokens
                self.final_matching_tokens["cost"] += cost
            else:
                self.skill_matching_tokens["input"] += input_tokens
                self.skill_matching_tokens["output"] += output_tokens
                self.skill_matching_tokens["cost"] += cost

            # logger.info(f"💰 {self.current_operation.upper()} TOKENS:")
            # logger.info(f"    Input: {input_tokens:,}")
            # logger.info(f"    Output: {output_tokens:,}")
            # logger.info(f"    Cost: ${cost:.6f}")

        except Exception as e:
            logger.error(f"Error in token tracking: {e}")

    def get_detailed_report(self):
        total_cost = (
            self.entity_extraction_tokens["cost"] +
            self.skill_matching_tokens["cost"] +
            self.final_matching_tokens["cost"]
        )
        return {
            "entity_extraction": self.entity_extraction_tokens,
            "skill_matching": self.skill_matching_tokens,
            "final_matching": self.final_matching_tokens,
            "totals": {
                "total_input_tokens": self.total_input,
                "total_output_tokens": self.total_output,
                "total_tokens": self.total_tokens,
                "total_cost": total_cost,
            },
        }