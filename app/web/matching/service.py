import json
from typing import Any, Dict, List, Optional, TypedDict

from langchain.callbacks.streaming_stdout import StreamingStdOutCallbackHandler
from langchain.chat_models import init_chat_model
from langgraph.graph import END, START, StateGraph

from app import logger, settings
from app.constants.response_messages import (CANDIDATE_MATCHED,
                                            CANDIDATE_NOT_MATCHED,
                                            EMPTY_RESULTS_FROM_SP)
from app.exception.llm_exceptions import handle_llm_exception
from app.web.matching.db import build_sp_params, call_sp_filter_candidates
from app.web.matching.models import (BatchSkillResult, CandidateResult,
                                    MatchFlagEnum, MatchingPromptResult,
                                    MatchingResult, ParsedQuery)
from app.web.matching.prompts import (batch_skill_prompt, entity_prompt,
                                    matching_prompt)
from app.web.matching.token_tracker import TokenUsageCallbackHandler


class GraphState(TypedDict):
    query: str
    uuid: str
    parsed: Optional[ParsedQuery]
    sql_query: Optional[str]
    sql_results: Optional[List[Dict[str, Any]]]
    error: Optional[str]


class MatchingService:
    def __init__(self, db_session_factory):
        self.db_session_factory = db_session_factory
        self.token_tracker = TokenUsageCallbackHandler()
        
        # Initialize LLM
        self.llm = init_chat_model(
            model=settings.openai_model_name,
            model_provider=settings.model_provider,
            temperature=0,
            api_key=settings.openai_api_key,
            callbacks=[StreamingStdOutCallbackHandler(), self.token_tracker],
        )
        
        self.parser = self.llm.with_structured_output(ParsedQuery)
        self.batch_skill_parser = self.llm.with_structured_output(BatchSkillResult)
        self.matching_parser = self.llm.with_structured_output(MatchingPromptResult)
        
        self.entity_prompt = entity_prompt
        self.batch_skill_prompt = batch_skill_prompt
        self.matching_prompt = matching_prompt
        
        # Initialize workflow
        self._setup_workflow()


    def _setup_workflow(self):
        workflow = StateGraph(GraphState)
        workflow.add_node("parse_query", self._parse_query_node)
        workflow.add_node("run_sp_query", self._query_sp_node)
        workflow.add_node("show_results", self._result_node)

        workflow.set_entry_point("parse_query")
        workflow.add_edge(START, "parse_query")
        workflow.add_conditional_edges(
            "parse_query",
            self._check_parsing_complete,
            {"run_sp_query": "run_sp_query", "retry": "parse_query", "none": END},
        )
        workflow.add_edge("run_sp_query", "show_results")
        workflow.add_edge("show_results", END)

        self.app = workflow.compile()

    async def _parse_query_node(self, state: GraphState) -> GraphState:
        query = state["query"]

        self.token_tracker.set_operation("entity_extraction")

        try:
            formatted_prompt = self.entity_prompt.format_messages(query=query)
            response = await self.parser.ainvoke(formatted_prompt)
            logger.info(f"1. Entity Response is: {response}\n")

            if not any([response.profession, response.experience_years, response.languages]):
                return {**state, "parsed": None}

            return {**state, "parsed": response}

        except Exception as e:
            logger.error(f"❌ Error parsing: {e}")
            handle_llm_exception(e, context="Parsing query")

    async def _query_sp_node(self, state: GraphState) -> GraphState:
        parsed = state.get("parsed")
        uuid = state.get("uuid")

        if not parsed:
            return state

        params = build_sp_params(parsed, uuid)
        logger.info(f"2. SP Params: {params}")

        try:
            async with self.db_session_factory() as session:
                results = await call_sp_filter_candidates(session, params)
                return {
                    **state,
                    "sql_query": str(params),
                    "sql_results": results,
                }
        except Exception as e:
            return {**state, "error": str(e)}

    def _parse_json_field(self, json_str: str) -> Optional[List[Dict[str, Any]]]:
        try:
            return json.loads(json_str)
        except Exception as e:
            logger.error(f"Error parsing JSON field: {e}")
            return None

    def _prepare_candidate_profile(self, candidate: Dict[str, Any], candidate_id: str, parsed: Optional[ParsedQuery] = None, extracted_skills: Optional[List[str]] = None) -> str:
        """Prepare a single candidate's profile for batch processing"""
        education_details = candidate.get("candidate_education_details") or []
        certificate_details = candidate.get("candidate_certificate_details") or []
        employment_details = candidate.get("candidate_employment_details") or []
        total_experience_years = candidate.get("total_experience_years") or 0
        about_details = candidate.get("about_me") or []
        gender_details = candidate.get("gender") or []
        country_name = candidate.get("country_name") or []
        state_name = candidate.get("state_name") or []
        city_name = candidate.get("city_name") or []

        profile_parts = []

        # 1. Profession
        if parsed and parsed.profession:
            profile_parts.append(f"Profession:\n{parsed.profession}")
        else:
            profile_parts.append("Profession:\nNo data found")

        # 2. Language
        if parsed and parsed.languages:
            profile_parts.append(f"Language:\n{parsed.languages}")
        else:
            profile_parts.append("Language:\nNo data found")

        # 3. Language Proficiency
        if parsed and parsed.language_proficiency:
            profile_parts.append(f"Language Proficiency:\n{parsed.language_proficiency}")
        else:
            profile_parts.append("Language Proficiency:\nB1")

        # 4. Total Experience
        if total_experience_years > 0:
            profile_parts.append(f"Total Experience:\n{total_experience_years} years")
        else:
            profile_parts.append("Total Experience:\nNo data found")

        # 5. About
        if about_details:
            profile_parts.append(f"About:\n{about_details}")
        else:
            profile_parts.append("About:\nNo data found")

        # 5. Country Name
        if country_name:
            profile_parts.append(f"Country Name:\n{country_name}")
        else:
            profile_parts.append("Country Name:\nNo data found")

        # 6. State Name
        if state_name:
            profile_parts.append(f"State Name:\n{state_name}")
        else:
            profile_parts.append("State Name:\nNo data found")

        # 7. City Name
        if city_name:
            profile_parts.append(f"City Name:\n{city_name}")
        else:
            profile_parts.append("City Name:\nNo data found")

        # 8. Gender
        if gender_details:
            profile_parts.append(f"Gender:\n{gender_details}")
        else:
            profile_parts.append("Gender:\nNo data found")

        # 9. Employment Details
        employment_texts = []
        if employment_details:
            for job in employment_details:
                parts = [
                    f"Title: {job.get('title', '')}",
                    f"Description: {job.get('description', '')}",
                    f"Start Date: {job.get('start_date', '')}",
                    f"End Date: {job.get('end_date', '')}",
                    f"Employment Type: {job.get('employment_type', '')}"
                ]
                job_text = "\n".join(p for p in parts if p.split(': ')[1].strip())
                if job_text:
                    employment_texts.append(job_text)
        
        if employment_texts:
            profile_parts.append("Employment Details:\n" + "\n\n".join(employment_texts))
        else:
            profile_parts.append("Employment Details:\nNo data found")

        # 10. Education Details
        education_texts = []
        if education_details:
            for edu in education_details:
                parts = [
                    f"Title: {edu.get('title', '')}",
                    f"Description: {edu.get('description', '')}",
                    f"Start Date: {edu.get('start_date', '')}",
                    f"End Date: {edu.get('end_date', '')}",
                    f"Education Course Level: {edu.get('education_course_level', '')}"
                ]
                edu_text = "\n".join(p for p in parts if p.split(': ')[1].strip())
                if edu_text:
                    education_texts.append(edu_text)

        if education_texts:
            profile_parts.append("Education Details:\n" + "\n\n".join(education_texts))
        else:
            profile_parts.append("Education Details:\nNo data found")

        # 11. Certificate Details
        certificate_texts = []
        if certificate_details:
            for cert in certificate_details:
                parts = [
                    f"Title: {cert.get('title', '')}",
                    f"Issued By: {cert.get('issued_by', '')}",
                    f"Date of Issuance: {cert.get('date_of_issuance', '')}",
                    f"Date of Expiry: {cert.get('date_of_expiry', '')}",
                    f"Content: {cert.get('content', '')}"
                ]
                cert_text = "\n".join(p for p in parts if p.split(': ')[1].strip())
                if cert_text:
                    certificate_texts.append(cert_text)
                
        if certificate_texts:
            profile_parts.append("Certificate Details:\n" + "\n\n".join(certificate_texts))
        else:
            profile_parts.append("Certificate Details:\nNo data found")

        full_description = "\n\n".join(profile_parts).strip()

        return f"CANDIDATE_ID: {candidate_id}\n\nPROFILE:\n{full_description}\n" + "=" * 50 + "\n\n"

    async def _extract_skills_for_all_candidates(self, candidates: List[Dict[str, Any]], query: str = "", parsed: Optional[ParsedQuery] = None) -> list:
        """Extract structured fields for all candidates in a single API call (no scoring)"""
        if not candidates:
            return []

        try:
            self.token_tracker.set_operation("skill_matching")

            all_profiles = ""
            candidate_ids = []

            for i, candidate in enumerate(candidates):
                candidate_id = str(i)
                candidate_ids.append(candidate_id)
                profile = self._prepare_candidate_profile(candidate, candidate_id, parsed)
                # logger.info(f"the full description is:\n{profile}\n")
                all_profiles += profile

            formatted_prompt = self.batch_skill_prompt.format_messages(
                profiles=all_profiles,
            )
            # logger.info(f"2. Skill Prompt: {formatted_prompt}\n\n\n")

            result = await self.batch_skill_parser.ainvoke(formatted_prompt)
            # logger.info(f"2. Skill Response is: {result.model_dump_json()}\n")
            logger.info(f"✅ Batch skills/entities extracted for {len(result.candidates)} candidates")

            return [c.model_dump() for c in result.candidates]

        except Exception as e:
            logger.error(f"❌ Batch skill extraction failed: {e}")
            return []

    def _get_fields_from_parsed(self, parsed: ParsedQuery) -> List[str]:
        fields = []
        if parsed.profession is not None:
            fields.append("profession")
        if parsed.experience_years is not None:
            fields.append("experience_years")
        if parsed.languages:
            fields.append("language")
            fields.append("language_proficiency")
        if parsed.skills:
            fields.append("skills")
        if parsed.education:
            fields.append("education")
        if parsed.certification:
            fields.append("certification")
        if parsed.gender:
            fields.append("gender")
        return fields

    def _filter_candidates_for_matching(
        self, 
        batch_candidates: List[Dict[str, Any]], 
        fields_to_use: List[str]
    ) -> List[Dict[str, Any]]:
        field_key_map = {
            # "profession": "profession",
            "experience_years": "batch_total_experience_years",
            "language": "batch_language",
            "language_proficiency": "batch_language_proficiency",
            "skills": "batch_skills",
            "education": "batch_education_level",
            "certification": "batch_certification",
            "gender": "batch_gender"
        }

        filtered_results = []
        for candidate in batch_candidates:
            filtered = {"candidate_id": candidate.get("candidate_id")}

            for field in fields_to_use:
                key = field_key_map.get(field)
                if key and key in candidate:
                    filtered[field] = candidate[key]

            # Always include language_proficiency (default to B1 if not parsed)
            if "language_proficiency" not in filtered:
                filtered["language_proficiency"] = candidate.get("batch_language_proficiency", "B1")

            filtered_results.append(filtered)
        return filtered_results

    async def _run_matching_prompt(self, response: str, batch_results: List[Dict]) -> List[Dict]:
        """
        Calls the matching_prompt LLM with the parsed query response and structured candidate data.
        Returns a list of dicts with component scores and total_score for each candidate.
        """

        # Clean and prepare the response (parsed data from entity extraction)
        cleaned_response = response.strip()
        formatted_response_log = "\n".join(cleaned_response.split())
        # logger.info(f"--> Parsed extraction (response):\n{formatted_response_log}\n")

        # Format structured candidate data into key=value format (NOT JSON)
        candidate_blocks = []
        for candidate in batch_results:
            lines = [f"{key} = {value}" for key, value in candidate.items()]
            block = "\n".join(lines)
            candidate_blocks.append(block)

        structured_candidate_str = "\n\n=============================\n\n".join(candidate_blocks)
        # logger.info(f"--> Structured candidate block:\n{structured_candidate_str}\n")

        # Format the prompt using the ChatPromptTemplate
        formatted_prompt = self.matching_prompt.format_messages(
            response=formatted_response_log,
            structured_candidate=structured_candidate_str
        )
        # logger.info(f"Matching prompt:\n{formatted_prompt}\n")

        # Call LLM with structured output parser
        result = await self.matching_parser.ainvoke(formatted_prompt)
        # logger.info(f"3. Matching Response: {result}\n")
        logger.info(f"✅ Matching completed for {len(result.candidates)} candidates")

        # Return parsed result as list of dicts
        return [c.model_dump() for c in result.candidates]


    async def _result_node(self, state: GraphState) -> GraphState:
        results = state.get("sql_results", [])
        query = state.get("query", "")
        parsed = state.get("parsed")

        if not results:
            logger.warning("⚠️ No results returned from SP.")
            return state

        # Parse JSON fields
        for r in results:
            for key in ["candidate_employment_details", "candidate_education_details", "candidate_certificate_details"]:
                if key in r and isinstance(r[key], str):
                    r[key] = self._parse_json_field(r[key])

        logger.info(f"🔄 Processing {len(results)} candidates for skill extraction...")

        # 1. Extract structured fields for all candidates (no scoring)
        batch_results = await self._extract_skills_for_all_candidates(results, query, parsed)
        
        # logger.info(f"**** {batch_results} ****")

        # Add profession / language data from parsed query
        for candidate in batch_results:
            candidate["profession"] = parsed.profession.capitalize() if parsed and parsed.profession else None

            # candidate["language"] = getattr(parsed, "languages", None)
            # candidate["batch_language_proficiency"] = getattr(parsed, "batch_language_proficiency", "B1")
            # Optional: ensure these fields exist (default to empty or None)
            candidate["batch_skills"] = candidate.get("batch_skills", [])
            candidate["batch_education_level"] = candidate.get("batch_education_level", [])
            candidate["batch_certification"] = candidate.get("batch_certification", [])
            candidate["batch_total_experience_years"] = candidate.get("batch_total_experience_years", 0.0)

        # 2. Run matching prompt to get scores and breakdowns
        self.token_tracker.set_operation("final_matching")
        fields_to_use = self._get_fields_from_parsed(parsed)
        filtered_batch_candidates = self._filter_candidates_for_matching(batch_results, fields_to_use)

        matching_results = await self._run_matching_prompt(str(parsed), filtered_batch_candidates)

        final_candidates = []
        for i, match in enumerate(matching_results):
            enriched = {
                **results[i],              # ← preserves uuid, name, etc.
                **batch_results[i],        # ← adds extracted fields
                "matching_score": {
                    "skills": match.get("skill_score"),
                    "experience_years": match.get("experience_years_score"),
                    "language_proficiency": match.get("language_proficiency_score"),
                    "education": match.get("education_level_score"),
                    "certification": match.get("certification_score"),
                },
                "total_score": match.get("total_score", 0)
            }
            final_candidates.append(enriched)

        return {**state, "sql_results": final_candidates}

    def _check_parsing_complete(self, ste: GraphState) -> str:
        if ste["parsed"] is not None:
            return "run_sp_query"
        else:
            return "none"

    async def process_matching_request(self, query: str, uuid: str = None) -> MatchingResult:
        """Main method to process matching request and save results to file"""

        initial_state = {
            "query": query,
            "uuid": uuid,
            "parsed": None,
            "sql_query": None,
            "sql_results": None,
            "error": None,
        }

        final_state = await self.app.ainvoke(initial_state)

        sql_results = final_state.get("sql_results", [])
        parsed = final_state.get("parsed")

        candidates: list[CandidateResult] = []

        # Scenario 1: entity_prompt returns all None
        if parsed is None:
            match_flag = MatchFlagEnum.CANDIDATE_NOT_MATCHED
            message = CANDIDATE_NOT_MATCHED

        else:
            # Scenario 2: entity_prompt returns entities but batch_skill_prompt (SP) returns no results
            if not sql_results:
                match_flag = MatchFlagEnum.EMPTY_RESULTS_FROM_SP
                message = EMPTY_RESULTS_FROM_SP

            else:
                # Scenario 3: both entity_prompt and batch_skill_prompt return results
                has_matching_scores = any("total_score" in r for r in sql_results)
                if has_matching_scores:
                    filtered_results = [r for r in sql_results if r.get("total_score", 0) >= 10]
                    results_to_save = sorted(filtered_results, key=lambda x: x.get("total_score", 0), reverse=True)
                    logger.info(f"✅ {len(results_to_save)} candidates matched with skills.")
                else:
                    results_to_save = sql_results
                    logger.info(f"✅ {len(results_to_save)} candidates returned (no skill match filtering).")

                if results_to_save:
                    match_flag = MatchFlagEnum.CANDIDATE_MATCHED
                    message = CANDIDATE_MATCHED
                else:
                    match_flag = MatchFlagEnum.EMPTY_RESULTS_FROM_SP
                    message = EMPTY_RESULTS_FROM_SP

                # Convert to response format
                for candidate in results_to_save:
                    # logger.info(f"--> \n\n {candidate} \n\n")
                    candidates.append(CandidateResult(
                        uuid=candidate.get("uuid"),
                        name=candidate.get("name"),
                        profile_photo=candidate.get("profile_photo"),
                        total_experience_years=candidate.get("total_experience_years"),
                        country=candidate.get("country_name"),
                        state=candidate.get("state_name"),
                        city=candidate.get("city_name"),
                        profession=candidate.get("profession"),
                        skills=candidate.get("skills", candidate.get("batch_skills", [])),
                        education=candidate.get("education", candidate.get("batch_education_level", [])),
                        certification=candidate.get("certification", candidate.get("batch_certification", [])),
                        language=candidate.get("language", candidate.get("batch_language")),
                        language_proficiency=candidate.get("language_proficiency", candidate.get("batch_language_proficiency")),
                        gender=candidate.get("gender", candidate.get("batch_gender")),
                        matching_score={
                            "skill_score": candidate.get("matching_score", {}).get("skills", 0),
                            "experience_years_score": candidate.get("matching_score", {}).get("experience_years", 0),
                            "language_proficiency_score": candidate.get("matching_score", {}).get("language_proficiency", 0),
                            "education_level_score": candidate.get("matching_score", {}).get("education", 0),
                            "certification_score": candidate.get("matching_score", {}).get("certification", 0),
                        },
                        total_score=candidate.get("total_score", 0),
                    ))

        # Get token usage report
        token_report = self.token_tracker.get_detailed_report()

        # Create result object
        result = MatchingResult(
            prompt=query,
            candidates=candidates,
            token_usage_report=token_report,
            match_flag=match_flag,
            message=message
        )

        logger.info("🎯 FINAL TOKEN USAGE AND COST ANALYSIS")
        logger.info(f"Entity extraction cost: ${token_report['entity_extraction']['cost']:.6f}")
        logger.info(f"Skill extraction cost: ${token_report['skill_matching']['cost']:.6f}")
        logger.info(f"Final matching cost: ${token_report['final_matching']['cost']:.6f}")
        logger.info(f"Total cost: ${token_report['totals']['total_cost']:.6f}")
        logger.info(f"Total tokens: {token_report['totals']['total_tokens']}")

        return result
