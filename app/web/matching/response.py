from typing import Any, Dict
from fastapi.responses import JSONResponse


class MatchingResponse(JSONResponse):
    """Response class for matching operations."""

    def __init__(
        self,
        message: str,
        payload: Dict[str, Any],
        status_code: int = 200,
    ):
        """
        Initialize the MatchingResponse.

        Args:
            message (str): A message describing the response.
            payload (Dict[str, Any]): The payload containing matching data.
            status_code (int, optional): HTTP status code.
        """
        super().__init__(
            content={
                "message": message,
                "status": status_code,
                "payload": payload,
            },
            status_code=status_code,
        )
