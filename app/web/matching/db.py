from typing import Any, Dict, List

import pandas as pd
from sqlalchemy import text

from app import logger
from app.constants.constant import (DEFAULT_LANGUAGE_PROFICIENCY_CONDITION,
                                    DEFAULT_STRING, DEFAULT_YEARS_EXPERIENCE,
                                    SP_LANGUAGE_CODE, SP_LIMIT, SP_NAME)
from app.web.matching.models import ParsedQuery


async def call_sp_filter_candidates(
    session, params: Dict[str, Any]
) -> List[Dict[str, Any]]:
    """
    Calls stored procedure defined in SP_NAME with given parameters.
    Returns a list of dicts representing candidate rows.
    """
    try:
        query = text(
            f"CALL {SP_NAME}(:uuid, :profession, :years, :language, "
            f":language_proficiency, :language_proficiency_condition, "
            f":gender, :language_code, :limit)"
        )
        result = await session.execute(query, params)
        df = pd.DataFrame(result.fetchall(), columns=result.keys())
        return df.to_dict(orient="records")
    except Exception as e:
        logger.error(f"❌ SQL Error: {e}")
        raise


def build_sp_params(parsed: ParsedQuery, uuid: str) -> Dict[str, Any]:
    """
    Builds the parameter dictionary for the stored procedure from a parsed query object.
    """
    return {
        "uuid": uuid,
        "profession": parsed.profession or DEFAULT_STRING,
        "years": int(parsed.experience_years or DEFAULT_YEARS_EXPERIENCE),
        "language": parsed.languages or DEFAULT_STRING,
        "language_proficiency": parsed.language_proficiency or DEFAULT_STRING,
        "language_proficiency_condition": parsed.language_proficiency_condition
        or DEFAULT_LANGUAGE_PROFICIENCY_CONDITION,
        "gender": parsed.gender if hasattr(parsed, "gender") else None,
        "language_code": SP_LANGUAGE_CODE,
        "limit": SP_LIMIT,
    }
