from fastapi import status
from fastapi.requests import Request
from fastapi_utils.cbv import cbv
from fastapi_utils.inferring_router import InferringRouter

from app import logger
from app.constants import response_messages
from app.web.monitor.response import HealthResponse

router = InferringRouter()


@cbv(router)
class Monitor:
    @router.get("/health")
    async def health_check(
        self,
        request: Request,
    ) -> HealthResponse:
        """
        Checks the health of a project.

        It returns 200 if the project is healthy.
        """
        logger.info("This is sample log")
        return HealthResponse(
            status=status.HTTP_200_OK,
            message=response_messages.HEALTH_SUCCESS,
            payload={},
        )
