from fastapi import status
from fastapi.responses import JSONResponse
from pydantic import BaseModel


class LLMResponse(BaseModel):
    message: str
    status: int
    payload: dict = {}

    @classmethod
    async def openai_unauthorized_response(cls):
        return JSONResponse(
            status_code=status.HTTP_401_UNAUTHORIZED,
            content={
                "message": "Invalid Authentication / Incorrect API key provided.",
                "status": status.HTTP_401_UNAUTHORIZED,
                "payload": {},
            },
        )

    @classmethod
    async def openai_rate_limit_response(cls):
        return JSONResponse(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            content={
                "message": "Rate limit reached for requests / You exceeded your current quota, please check your plan and billing details.",
                "status": status.HTTP_429_TOO_MANY_REQUESTS,
                "payload": {},
            },
        )
