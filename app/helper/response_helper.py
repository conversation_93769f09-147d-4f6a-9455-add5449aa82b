from fastapi import status
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from app.constants import response_messages


class BaseResponse(BaseModel):
    message: str
    status: int
    payload: dict = {}

    @classmethod
    async def request_exception_response(cls, req, exc):
        return JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            content=jsonable_encoder(
                {
                    "message": response_messages.VALIDATION_ERROR,
                    "payload": str(exc),
                    "status_code": status.HTTP_422_UNPROCESSABLE_ENTITY,
                }
            ),
        )

    @classmethod
    async def custom_exception_response(
        cls, message, status_code=status.HTTP_422_UNPROCESSABLE_ENTITY
    ):
        return JSONResponse(
            status_code=status_code,
            content={
                "message": message,
                "status": status_code,
                "payload": {},
            },
        )

    @classmethod
    async def server_error_response(cls):
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content={
                "message": response_messages.SOMETHING_WENT_WRONG,
                "status": status.HTTP_500_INTERNAL_SERVER_ERROR,
                "payload": {},
            },
        )

    @classmethod
    async def bad_request_response(cls, message: str):
        return JSONResponse(
            status_code=status.HTTP_400_BAD_REQUEST,
            content={
                "message": message,
                "status": status.HTTP_400_BAD_REQUEST,
                "payload": {},
            },
        )
