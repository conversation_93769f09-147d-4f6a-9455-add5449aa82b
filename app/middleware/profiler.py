import time
import uuid
from enum import Enum

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware

from app import logger, settings
from app.exception import CustomException
from app.helper.response_helper import BaseResponse


class CustomHeaders(str, Enum):
    KEYCLOAK_UUID = "x-keycloak-uuid"
    USER_ID = "x-identity-user-id"
    REQ_UUID = "x-req-uuid"
    ENTERPRISE_UUID = "x-enterprise-uuid"
    ENTERPRISE_ID = "x-enterprise-id"
    ENTERPRISE_ROLE_ID = "x-enterprise-role-id"
    APP_TYPE_ID = "x-app-type-id"

class ProfilerMiddleware(BaseHTTPMiddleware):
    def __init__(self, app):
        super().__init__(app)

    async def dispatch(self, request: Request, call_next):
        """
        Middleware performs:
        1. Add request_id to track execution
        2. Optionally use incoming x-req-uuid header
        3. Log processing time
        4. Handle server-side exceptions globally
        """
        # Get x-req-uuid from headers or generate one
        incoming_req_uuid = request.headers.get(CustomHeaders.REQ_UUID.value)
        request_id = incoming_req_uuid or str(uuid.uuid4())

        request.state.request_id = request_id  # For use across the app

        start_time = time.time()

        try:
            response = await call_next(request)

            process_time = time.time() - start_time
            response.headers["X-Process-Time"] = str(process_time)
            response.headers["Request-ID"] = request_id

            if process_time >= 2:
                logger.warning(
                    f"RequestID: {request_id} took more than 2 seconds. Please review performance."
                )

            return response

        except CustomException as ce:
            resp = await BaseResponse.custom_exception_response(ce.name)
            return resp
        except Exception as e:
            if settings.environment == "dev":
                import traceback
                print(traceback.format_exc())

            logger.error(
                f"RequestID: {request_id} -> Path: {request.url.path} | Error: {str(e)}"
            )
            return await BaseResponse.server_error_response()
