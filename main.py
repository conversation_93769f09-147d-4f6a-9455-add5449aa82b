import uvicorn

from app import settings


def main() -> None:
    """Entrypoint of the application."""
    uvicorn.run(
        "app.application:get_app",
        workers=settings.workers_count,
        host=settings.host,
        port=settings.port,
        reload=True if settings.environment != "production" else False,
        log_level=settings.log_level.value.lower(),
        factory=True,
    )


if __name__ == "__main__":
    main()
